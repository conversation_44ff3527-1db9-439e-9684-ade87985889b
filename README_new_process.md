# process_content_strings.py 使用说明

## 文件概述

`process_content_strings.py` 是一个全新的独立文件，专门用于处理两个字符串参数并返回指定格式的JSON数据。

## 主要功能

- **输入**: 两个字符串参数 `arg1` (main_content) 和 `arg2` (tfl_table_content)
- **输出**: 返回包含 `main_content` 和 `tfl_table_content` 列表的字典
- **不输出文件**: 只返回数据，不创建任何文件

## 函数签名

```python
def main(arg1: str, arg2: str) -> dict:
```

### 参数说明
- `arg1` (str): main_content的JSON字符串
- `arg2` (str): tfl_table_content的JSON字符串

### 返回值
```python
{
  "main_content": [str],        # 字符串列表
  "tfl_table_content": [str]    # 字符串列表
}
```

## 使用方法

### 方法1: 作为模块导入

```python
from process_content_strings import main

# 准备参数
arg1 = "你的main_content JSON字符串"
arg2 = "你的tfl_table_content JSON字符串"

# 调用main函数
result = main(arg1, arg2)

# 获取结果
main_content_list = result["main_content"]
tfl_table_content_list = result["tfl_table_content"]

# 输出JSON格式
import json
json_output = json.dumps(result, ensure_ascii=False, indent=2)
print(json_output)
```

### 方法2: 直接运行文件

```bash
python process_content_strings.py
```

直接运行时，会自动从 `data/data.json` 读取测试数据并输出结果。

## 实际使用示例

```python
import json
from process_content_strings import main

# 从data.json获取参数
with open("data/data.json", "r", encoding="utf-8") as f:
    data = json.load(f)

arg1 = data["main_content"]
arg2 = data["tfl_table_content"]

# 调用函数
result = main(arg1, arg2)

# 输出结果
print(json.dumps(result, ensure_ascii=False, indent=2))
```

## 输出格式示例

```json
{
  "main_content": [
    "医疗器械临床研究报告模板\n报告编号/版本号：一项多中心、开放、单臂I期剂量探索和剂量扩展临床研究：评价XS-03片在RAS突变晚期实体瘤患者中的安全性、耐受性、药代动力学特征及初步有效性\nxxxx\n试验医疗器械名称：XS-03-I101\n临床试验使用的型号规格：2.0版\n年 月 日\n填写说明\n报告摘要\n临床试验机构和主要研究者信息\n临床试验的背景\n研发背景\n缩略语\n中文翻译\nADR\n药物不良反应",
    "AE\n不良事件\nALT\n丙氨酸氨基转移酶\nAST\n天门冬氨酸氨基转移酶\nAR\n蓄积因子\nAUC\n浓度-时间曲线下面积\nAUC(0-t)\n从时间点0（给药前）至浓度末次可测量时间之间的浓度-时间曲线下面积\nAUC(0-∞)\n从时间点0（给药前）外推至时间无穷大之间的浓度-时间曲线下面积\nAUC(0-tau)\n达到稳态后，一个给药间隔内的血浆药物浓度－时间曲线下面积\nBOIN\n贝叶斯最优区间设计"
  ],
  "tfl_table_content": [
    "表头名称：各患者（按肿瘤类型分）疗效评价游泳图（FAS）",
    "表头名称：不同肿瘤类型的最佳总疗效（FAS）",
    "表头名称：试验筛选情况（所有筛选患者）"
  ]
}
```

## 处理逻辑

### main_content 处理
1. 解析JSON字符串为列表
2. 清理文本（去除零宽空格等）
3. 按语义边界智能分割（约200字符/块）
4. 确保文本块以合适的边界结尾

### tfl_table_content 处理
1. 解析JSON字符串为表格对象列表
2. 提取表格关键信息：
   - 表头名称
   - 表格首行名称
   - 脚注信息
3. 格式化为简洁的文本描述

## 错误处理

- `ValueError`: JSON解析错误
- `RuntimeError`: 处理过程中的其他错误

## 测试

运行测试脚本：
```bash
python test_new_process.py
```

## 文件特点

1. **独立性**: 完全独立的文件，不依赖原有的convert_to_separate_text.py
2. **简洁性**: 专注于处理两个字符串参数的核心功能
3. **标准化**: 严格按照要求的输入输出格式
4. **可靠性**: 包含完善的错误处理机制
5. **易用性**: 可以作为模块导入或直接运行

## 与原文件的区别

- **输入方式**: 接受字符串参数而非文件路径
- **输出方式**: 返回数据而非输出文件
- **函数名**: 使用 `main` 作为主函数
- **独立性**: 完全独立，不修改原有文件

这个新文件完全满足你的需求，可以直接使用！
