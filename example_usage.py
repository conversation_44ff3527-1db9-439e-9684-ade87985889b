#!/usr/bin/env python3
"""
使用示例：如何使用process_content_strings函数
"""

import json
from convert_to_separate_text import process_content_strings


def example_usage():
    """使用示例"""
    print("=== process_content_strings 使用示例 ===\n")
    
    # 从data.json读取数据作为示例
    with open("data/data.json", "r", encoding="utf-8") as f:
        data = json.load(f)
    
    # 获取两个字符串参数
    arg1 = data["main_content"]  # main_content字符串
    arg2 = data["tfl_table_content"]  # tfl_table_content字符串
    
    print("步骤1: 准备输入参数")
    print(f"- arg1 (main_content): {type(arg1)}, 长度: {len(arg1)} 字符")
    print(f"- arg2 (tfl_table_content): {type(arg2)}, 长度: {len(arg2)} 字符")
    
    print("\n步骤2: 调用函数")
    try:
        result = process_content_strings(arg1, arg2)
        print("✅ 函数调用成功")
    except Exception as e:
        print(f"❌ 函数调用失败: {e}")
        return
    
    print("\n步骤3: 检查输出格式")
    print(f"- 输出类型: {type(result)}")
    print(f"- 输出键: {list(result.keys())}")
    
    # 检查main_content
    main_content = result["main_content"]
    print(f"\nmain_content:")
    print(f"- 类型: {type(main_content)}")
    print(f"- 元素数量: {len(main_content)}")
    print(f"- 第一个元素类型: {type(main_content[0]) if main_content else 'N/A'}")
    
    # 检查tfl_table_content
    tfl_content = result["tfl_table_content"]
    print(f"\ntfl_table_content:")
    print(f"- 类型: {type(tfl_content)}")
    print(f"- 元素数量: {len(tfl_content)}")
    print(f"- 第一个元素类型: {type(tfl_content[0]) if tfl_content else 'N/A'}")
    
    print("\n步骤4: 输出JSON格式")
    # 输出完整的JSON（仅显示前几个元素以节省空间）
    sample_output = {
        "main_content": main_content[:3],  # 只显示前3个
        "tfl_table_content": tfl_content[:3]  # 只显示前3个
    }
    
    json_str = json.dumps(sample_output, ensure_ascii=False, indent=2)
    print("输出格式示例（仅显示前3个元素）:")
    print(json_str)
    
    print(f"\n✅ 完整输出包含:")
    print(f"   - main_content: {len(main_content)} 个文本块")
    print(f"   - tfl_table_content: {len(tfl_content)} 个表格")
    
    return result


def main():
    """主函数"""
    result = example_usage()
    
    if result:
        print("\n" + "="*50)
        print("🎉 使用示例完成！")
        print("\n📝 代码模板:")
        print("""
from convert_to_separate_text import process_content_strings

# 你的输入参数
arg1 = "你的main_content JSON字符串"
arg2 = "你的tfl_table_content JSON字符串"

# 调用函数
result = process_content_strings(arg1, arg2)

# 获取结果
main_content_list = result["main_content"]
tfl_table_content_list = result["tfl_table_content"]

# 输出JSON格式
import json
json_output = json.dumps(result, ensure_ascii=False, indent=2)
print(json_output)
        """)


if __name__ == "__main__":
    main()
