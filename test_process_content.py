#!/usr/bin/env python3
"""
测试process_content_strings函数
"""

import json
from convert_to_separate_text import process_content_strings


def test_with_data_json():
    """使用data.json中的数据测试函数"""
    print("=== 测试process_content_strings函数 ===\n")
    
    # 读取data.json文件
    with open("data/data.json", "r", encoding="utf-8") as f:
        data = json.load(f)
    
    # 获取两个参数
    main_content_str = data["main_content"]
    tfl_table_content_str = data["tfl_table_content"]
    
    print("输入参数:")
    print(f"- main_content_str类型: {type(main_content_str)}")
    print(f"- main_content_str长度: {len(main_content_str)} 字符")
    print(f"- tfl_table_content_str类型: {type(tfl_table_content_str)}")
    print(f"- tfl_table_content_str长度: {len(tfl_table_content_str)} 字符")
    
    try:
        # 调用函数
        result = process_content_strings(main_content_str, tfl_table_content_str)
        
        print("\n✅ 函数执行成功！")
        print("\n输出结果:")
        print(f"- 结果类型: {type(result)}")
        print(f"- 结果键: {list(result.keys())}")
        
        # 检查main_content
        main_content = result["main_content"]
        print(f"\nmain_content:")
        print(f"- 类型: {type(main_content)}")
        print(f"- 长度: {len(main_content)} 个文本块")
        
        if main_content:
            lengths = [len(chunk) for chunk in main_content]
            print(f"- 平均长度: {sum(lengths) / len(lengths):.1f} 字符")
            print(f"- 长度范围: {min(lengths)} - {max(lengths)} 字符")
            
            print("\n前3个文本块预览:")
            for i in range(min(3, len(main_content))):
                chunk = main_content[i]
                print(f"  块{i+1} ({len(chunk)}字符): {chunk[:100]}...")
        
        # 检查tfl_table_content
        tfl_content = result["tfl_table_content"]
        print(f"\ntfl_table_content:")
        print(f"- 类型: {type(tfl_content)}")
        print(f"- 长度: {len(tfl_content)} 个表格")
        
        if tfl_content:
            print("\n前3个表格预览:")
            for i in range(min(3, len(tfl_content))):
                table_text = tfl_content[i]
                print(f"  表格{i+1}: {table_text}")
        
        # 输出JSON格式示例
        print("\n=== JSON格式输出示例 ===")
        sample_result = {
            "main_content": main_content[:2] if len(main_content) >= 2 else main_content,
            "tfl_table_content": tfl_content[:2] if len(tfl_content) >= 2 else tfl_content
        }
        
        json_output = json.dumps(sample_result, ensure_ascii=False, indent=2)
        print(json_output)
        
        return result
        
    except Exception as e:
        print(f"❌ 函数执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主函数"""
    result = test_with_data_json()
    
    if result:
        print("\n=== 测试完成 ===")
        print("✅ process_content_strings函数工作正常")
        print("✅ 输出格式符合要求")
        print("\n使用方法:")
        print("from convert_to_separate_text import process_content_strings")
        print("result = process_content_strings(arg1, arg2)")
        print("# result 是包含 'main_content' 和 'tfl_table_content' 键的字典")
    else:
        print("\n❌ 测试失败")


if __name__ == "__main__":
    main()
