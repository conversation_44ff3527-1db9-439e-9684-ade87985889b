#!/usr/bin/env python3
"""
测试新的process_content_strings.py文件
"""

import json
from process_content_strings import main


def test_main_function():
    """测试main函数"""
    print("=== 测试新的process_content_strings.py ===\n")
    
    # 读取data.json文件
    try:
        with open("data/data.json", "r", encoding="utf-8") as f:
            data = json.load(f)
    except FileNotFoundError:
        print("❌ 错误：找不到data/data.json文件")
        return None
    
    # 获取两个参数
    arg1 = data["main_content"]
    arg2 = data["tfl_table_content"]
    
    print("输入参数:")
    print(f"- arg1 (main_content) 类型: {type(arg1)}")
    print(f"- arg1 长度: {len(arg1)} 字符")
    print(f"- arg2 (tfl_table_content) 类型: {type(arg2)}")
    print(f"- arg2 长度: {len(arg2)} 字符")
    
    try:
        # 调用main函数
        result = main(arg1, arg2)
        
        print("\n✅ main函数执行成功！")
        print("\n输出结果:")
        print(f"- 结果类型: {type(result)}")
        print(f"- 结果键: {list(result.keys())}")
        
        # 验证输出格式
        assert isinstance(result, dict), "结果应该是字典类型"
        assert "main_content" in result, "结果应该包含main_content键"
        assert "tfl_table_content" in result, "结果应该包含tfl_table_content键"
        assert isinstance(result["main_content"], list), "main_content应该是列表类型"
        assert isinstance(result["tfl_table_content"], list), "tfl_table_content应该是列表类型"
        
        # 检查main_content
        main_content = result["main_content"]
        print(f"\nmain_content:")
        print(f"- 类型: {type(main_content)}")
        print(f"- 长度: {len(main_content)} 个文本块")
        
        if main_content:
            # 验证每个元素都是字符串
            for i, item in enumerate(main_content):
                assert isinstance(item, str), f"main_content[{i}]应该是字符串类型"
            
            lengths = [len(chunk) for chunk in main_content]
            print(f"- 平均长度: {sum(lengths) / len(lengths):.1f} 字符")
            print(f"- 长度范围: {min(lengths)} - {max(lengths)} 字符")
            
            print("\n前2个文本块预览:")
            for i in range(min(2, len(main_content))):
                chunk = main_content[i]
                print(f"  块{i+1} ({len(chunk)}字符): {chunk[:80]}...")
        
        # 检查tfl_table_content
        tfl_content = result["tfl_table_content"]
        print(f"\ntfl_table_content:")
        print(f"- 类型: {type(tfl_content)}")
        print(f"- 长度: {len(tfl_content)} 个表格")
        
        if tfl_content:
            # 验证每个元素都是字符串
            for i, item in enumerate(tfl_content):
                assert isinstance(item, str), f"tfl_table_content[{i}]应该是字符串类型"
            
            print("\n前3个表格预览:")
            for i in range(min(3, len(tfl_content))):
                table_text = tfl_content[i]
                print(f"  表格{i+1}: {table_text}")
        
        # 输出JSON格式示例
        print("\n=== 输出格式验证 ===")
        sample_result = {
            "main_content": main_content[:1] if main_content else [],
            "tfl_table_content": tfl_content[:2] if tfl_content else []
        }
        
        json_output = json.dumps(sample_result, ensure_ascii=False, indent=2)
        print("JSON格式输出示例:")
        print(json_output)
        
        print("\n✅ 所有验证通过！")
        return result
        
    except Exception as e:
        print(f"❌ main函数执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 测试无效JSON
    try:
        result = main("invalid json", "[]")
        print("❌ 应该抛出JSON解析错误")
    except ValueError as e:
        print(f"✅ 正确捕获JSON解析错误: {e}")
    except Exception as e:
        print(f"❌ 意外错误: {e}")
    
    # 测试空字符串
    try:
        result = main("[]", "[]")
        print("✅ 空列表处理成功")
        print(f"   结果: {result}")
    except Exception as e:
        print(f"❌ 空列表处理失败: {e}")


def main_test():
    """主测试函数"""
    result = test_main_function()
    test_error_handling()
    
    if result:
        print("\n" + "="*50)
        print("🎉 新文件测试完成！")
        print("\n📝 使用方法:")
        print("""
from process_content_strings import main

# 准备参数
arg1 = "你的main_content JSON字符串"
arg2 = "你的tfl_table_content JSON字符串"

# 调用main函数
result = main(arg1, arg2)

# 获取结果
main_content_list = result["main_content"]
tfl_table_content_list = result["tfl_table_content"]
        """)
        
        print("✅ 新文件功能正常")
        print("✅ 输出格式符合要求")
        print("✅ 错误处理完善")
    else:
        print("\n❌ 测试失败")


if __name__ == "__main__":
    main_test()
