"""
将data.json转换为两个独立的文本格式
保持main_content和tfl_table_content作为两个独立的键值对
"""

import json
import re


def clean_text(text: str) -> str:
    """清理文本，去除无用的ZWSP等字符"""
    if not isinstance(text, str):
        return str(text)

    # 去除零宽空格 (ZWSP) 和其他不可见字符
    text = text.replace("\u200b", "")  # ZWSP
    text = text.replace("\ufeff", "")  # BOM
    text = text.replace("\u200c", "")  # ZWNJ
    text = text.replace("\u200d", "")  # ZWJ

    # 去除多余的空白字符和换行符
    text = re.sub(r"\s+", " ", text)
    text = text.strip()

    return text


def split_text_by_semantic_boundaries(text: str, target_length: int = 200) -> list[str]:
    """按语义边界分割文本，每段大约target_length字符，确保以换行符或句号结尾"""
    if not text:
        return []

    # 首先按换行符分割
    lines = text.split("\n")
    result = []
    current_chunk = ""

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 如果当前行本身就很长，需要进一步分割
        if len(line) > target_length * 1.5:
            # 如果当前chunk不为空，先保存（确保以换行符结尾）
            if current_chunk:
                if not current_chunk.endswith("\n"):
                    current_chunk += "\n"
                result.append(current_chunk.rstrip())
                current_chunk = ""

            # 按中文句号分割长行
            sentences = line.split("。")
            temp_chunk = ""

            for i, sentence in enumerate(sentences):
                sentence = sentence.strip()
                if not sentence:
                    continue

                # 如果不是最后一个句子，加回句号
                if i < len(sentences) - 1:
                    sentence += "。"

                # 检查添加这个句子后是否超长
                if temp_chunk and len(temp_chunk + sentence) > target_length:
                    # 确保以句号结尾
                    if not temp_chunk.endswith("。"):
                        temp_chunk += "。"
                    result.append(temp_chunk.strip())
                    temp_chunk = sentence
                else:
                    temp_chunk = temp_chunk + sentence if temp_chunk else sentence

            # 保存剩余的temp_chunk
            if temp_chunk:
                current_chunk = temp_chunk
        else:
            # 检查添加这一行后是否超长
            test_chunk = current_chunk + "\n" + line if current_chunk else line
            if len(test_chunk) > target_length:
                # 保存当前chunk，确保语义完整
                if current_chunk:
                    # 检查当前chunk是否以合适的边界结尾
                    if not (current_chunk.endswith("。") or current_chunk.endswith("\n")):
                        # 如果当前行以句号结尾，可以包含进来
                        if line.endswith("。") and len(test_chunk) <= target_length * 1.2:
                            current_chunk = test_chunk
                            continue
                    result.append(current_chunk.strip())
                current_chunk = line
            else:
                current_chunk = test_chunk

    # 保存最后的chunk
    if current_chunk:
        result.append(current_chunk.strip())

    # 后处理：确保每个chunk都以合适的边界结尾
    processed_result = []
    for chunk in result:
        if chunk and not (chunk.endswith("。") or chunk.endswith("\n")):
            # 如果不以句号或换行符结尾，尝试在合适的位置添加
            if "。" in chunk:
                # 找到最后一个句号的位置
                last_period = chunk.rfind("。")
                if last_period > len(chunk) * 0.7:  # 如果句号在后70%的位置
                    chunk = chunk[: last_period + 1]
            # 如果仍然不以句号结尾，保持原样（可能是列表项等）
        processed_result.append(chunk)

    return processed_result


def convert_main_content_to_text_list(main_content_list: list[str], target_length: int = 200) -> list[str]:
    """将main_content列表转换为按语义分割的文本字符串列表"""
    print("正在转换main_content为语义分割的文本列表...")

    # 清理每个条目并过滤空内容
    cleaned_items = []
    for item in main_content_list:
        cleaned = clean_text(item)
        if cleaned and cleaned not in ["\u200b", ""]:  # 过滤空内容和特殊字符
            cleaned_items.append(cleaned)

    # 用换行符连接所有内容
    merged_content = "\n".join(cleaned_items)

    # 按语义边界分割
    text_chunks = split_text_by_semantic_boundaries(merged_content, target_length)

    print(f"main_content转换完成：{len(cleaned_items)} 个原始条目 -> {len(text_chunks)} 个语义分割的文本块")
    if text_chunks:
        print(f"平均每块长度：{sum(len(chunk) for chunk in text_chunks) / len(text_chunks):.1f} 字符")

    return text_chunks


def format_table_as_text(table: dict) -> str:
    """将单个表格对象转换为文本格式"""
    lines = []

    # 表头名称
    name = table.get("name", "")
    if name:
        lines.append(f"表头名称：{clean_text(name)}")

    # 表格的首行名称（headers）
    headers = []

    # 优先从table_summary中获取headers（优化后的数据）
    if "table_summary" in table and "headers" in table["table_summary"]:
        headers = table["table_summary"]["headers"]
    # 如果没有table_summary，尝试从原始tblData中获取第一行作为headers
    elif "tblData" in table and table["tblData"] and len(table["tblData"]) > 0:
        headers = table["tblData"][0]  # 第一行通常是表头

    if headers:
        # 清理headers并用 | 分隔
        clean_headers = [clean_text(header) for header in headers if header and str(header).strip()]
        if clean_headers:
            headers_text = " | ".join(clean_headers)
            lines.append(f"表格的首行名称：{headers_text}")

    # 脚注
    footnote = table.get("footnote", "")
    if footnote:
        clean_footnote = clean_text(footnote)
        lines.append(f"脚注：{clean_footnote}")

    # 如果没有任何内容，至少保留表头名称
    if not lines and name:
        lines.append(f"表头名称：{clean_text(name)}")

    return "\n".join(lines)


def convert_tfl_content_to_text_list(tfl_list: list[dict]) -> list[str]:
    """将TFL表格列表转换为文本字符串列表"""
    print("正在转换tfl_table_content...")

    text_tables = []
    for i, table in enumerate(tfl_list):
        try:
            table_text = format_table_as_text(table)
            if table_text.strip():  # 只添加非空的文本
                text_tables.append(table_text)
            else:
                # 如果格式化失败，至少保留基本信息
                basic_text = f"表头名称：{table.get('name', f'表格{i + 1}')}"
                text_tables.append(basic_text)
        except Exception as e:
            print(f"警告：转换第 {i} 个表格时出错: {e}")
            # 创建一个基本的文本表示
            basic_text = f"表头名称：{table.get('name', f'表格{i + 1}')}"
            text_tables.append(basic_text)

    print(f"tfl_table_content转换完成：{len(text_tables)} 个表格")
    return text_tables


def convert_from_original_data(input_file: str) -> dict[str, list[str]]:
    """从原始data.json文件转换数据，保持两个独立的键值对"""
    print(f"正在从原始文件 {input_file} 加载数据...")

    with open(input_file, encoding="utf-8") as f:
        original_data = json.load(f)

    # 解析main_content
    main_content_str = original_data["main_content"]
    main_content_list = json.loads(main_content_str)

    # 解析tfl_table_content
    tfl_content_str = original_data["tfl_table_content"]
    tfl_list = json.loads(tfl_content_str)

    # 转换main_content为语义分割的文本字符串列表
    main_content_text_list = convert_main_content_to_text_list(main_content_list)

    # 转换tfl_table_content为文本字符串列表
    tfl_content_text_list = convert_tfl_content_to_text_list(tfl_list)

    # 返回保持原有结构的结果
    result = {
        "main_content": main_content_text_list,  # 字符串列表
        "tfl_table_content": tfl_content_text_list,  # 字符串列表
    }

    return result


def save_separate_text_format(data: dict[str, list[str]], output_file: str):
    """保存分离的文本格式数据"""
    print(f"正在保存分离文本格式数据到 {output_file}...")

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def preview_separate_results(data: dict[str, list[str]]):
    """预览转换结果"""
    print("\n=== 分离格式转换结果预览 ===")

    main_content = data["main_content"]
    tfl_content = data["tfl_table_content"]

    print("1. main_content (字符串列表):")
    print(f"   类型: {type(main_content)}")
    print(f"   文本块数量: {len(main_content)}")
    if main_content:
        lengths = [len(chunk) for chunk in main_content]
        print(f"   平均长度: {sum(lengths) / len(lengths):.1f} 字符")
        print(f"   长度范围: {min(lengths)} - {max(lengths)} 字符")
        print("   前3个文本块预览:")
        for i in range(min(3, len(main_content))):
            chunk = main_content[i]
            print(f"     块{i + 1} ({len(chunk)}字符): {chunk[:100]}...")

    print("\n2. tfl_table_content (字符串列表):")
    print(f"   类型: {type(tfl_content)}")
    print(f"   表格数量: {len(tfl_content)}")

    if tfl_content:
        print("\n前3个表格预览:")
        for i in range(min(3, len(tfl_content))):
            table_text = tfl_content[i]
            print(f"\n   表格 {i + 1}:")
            print(f"   {table_text}")


def process_content_strings(main_content_str: str, tfl_table_content_str: str) -> dict:
    """
    处理两个字符串参数，返回指定格式的JSON

    Args:
        main_content_str: main_content的JSON字符串
        tfl_table_content_str: tfl_table_content的JSON字符串

    Returns:
        dict: 包含main_content和tfl_table_content列表的字典
    """
    try:
        # 解析main_content
        main_content_list = json.loads(main_content_str)

        # 解析tfl_table_content
        tfl_list = json.loads(tfl_table_content_str)

        # 转换main_content为语义分割的文本字符串列表
        main_content_text_list = convert_main_content_to_text_list(main_content_list)

        # 转换tfl_table_content为文本字符串列表
        tfl_content_text_list = convert_tfl_content_to_text_list(tfl_list)

        # 返回指定格式的结果
        result = {
            "main_content": main_content_text_list,
            "tfl_table_content": tfl_content_text_list,
        }

        return result

    except json.JSONDecodeError as e:
        raise ValueError(f"JSON解析错误: {e}")
    except Exception as e:
        raise RuntimeError(f"处理过程中发生错误: {e}")


def main():
    """主函数"""
    print("=== 转换为分离的文本格式 ===\n")

    input_file = "data/data.json"
    output_file = "data_separated_text.json"

    try:
        # 从原始文件转换
        converted_data = convert_from_original_data(input_file)

        # 预览结果
        preview_separate_results(converted_data)

        # 保存结果
        save_separate_text_format(converted_data, output_file)

        print("\n✅ 转换完成！")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        print("最终格式:")
        print("  - main_content: 语义分割的文本字符串列表")
        print("  - tfl_table_content: 表格文本字符串列表")

        # 验证结果
        print("\n=== 验证结果 ===")
        with open(output_file, encoding="utf-8") as f:
            result_data = json.load(f)

        print("✅ 验证成功：")
        print(f"   - 数据结构: {list(result_data.keys())}")
        print(f"   - main_content类型: {type(result_data['main_content'])}")
        print(f"   - main_content长度: {len(result_data['main_content'])} 个文本块")
        print(f"   - tfl_table_content类型: {type(result_data['tfl_table_content'])}")
        print(f"   - tfl_table_content长度: {len(result_data['tfl_table_content'])} 个表格")

        # 统计main_content的长度分布
        main_list = result_data["main_content"]
        if main_list:
            lengths = [len(chunk) for chunk in main_list]
            print(f"   - main_content平均长度: {sum(lengths) / len(lengths):.1f} 字符")
            print(f"   - main_content长度范围: {min(lengths)} - {max(lengths)} 字符")

        # 统计不同类型的表格
        tfl_list = result_data["tfl_table_content"]
        if tfl_list:
            tables_with_headers = sum(1 for table in tfl_list if "表格的首行名称：" in table)
            tables_with_footnotes = sum(1 for table in tfl_list if "脚注：" in table)

            print(f"   - 包含表头的表格: {tables_with_headers}")
            print(f"   - 包含脚注的表格: {tables_with_footnotes}")

    except FileNotFoundError:
        print(f"❌ 错误：找不到输入文件 {input_file}")
    except Exception as e:
        print(f"❌ 转换过程中发生错误: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
