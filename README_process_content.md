# process_content_strings 函数使用说明

## 功能描述

`process_content_strings` 函数接受两个字符串参数（`main_content` 和 `tfl_table_content`），处理后返回指定格式的JSON数据，不输出文件。

## 函数签名

```python
def process_content_strings(main_content_str: str, tfl_table_content_str: str) -> dict:
```

## 参数说明

- `main_content_str` (str): main_content的JSON字符串
- `tfl_table_content_str` (str): tfl_table_content的JSON字符串

## 返回值

返回一个字典，包含以下键：

```python
{
  "main_content": [str],        # 字符串列表
  "tfl_table_content": [str]    # 字符串列表
}
```

## 使用示例

### 基本使用

```python
from convert_to_separate_text import process_content_strings

# 准备输入参数（从data.json获取）
import json
with open("data/data.json", "r", encoding="utf-8") as f:
    data = json.load(f)

arg1 = data["main_content"]        # main_content字符串
arg2 = data["tfl_table_content"]   # tfl_table_content字符串

# 调用函数
result = process_content_strings(arg1, arg2)

# 获取结果
main_content_list = result["main_content"]
tfl_table_content_list = result["tfl_table_content"]

# 输出JSON格式
json_output = json.dumps(result, ensure_ascii=False, indent=2)
print(json_output)
```

### 输出格式示例

```json
{
  "main_content": [
    "医疗器械临床研究报告模板\n报告编号/版本号：一项多中心、开放、单臂I期剂量探索和剂量扩展临床研究：评价XS-03片在RAS突变晚期实体瘤患者中的安全性、耐受性、药代动力学特征及初步有效性\nxxxx\n试验医疗器械名称：XS-03-I101\n临床试验使用的型号规格：2.0版\n年 月 日\n填写说明\n报告摘要\n临床试验机构和主要研究者信息\n临床试验的背景\n研发背景\n缩略语\n中文翻译\nADR\n药物不良反应",
    "C-QTc分析集（C-QTcS）在2 mg和5 mg组无数据，其他剂量组均为100%纳入，总体纳入率为87.5%（14/16）。分析集定义遵循：FAS用于受试者分布和基线特征；EAS需满足基线及至少一次基线后肿瘤评估；SS用于安全性分析；DLTS需完成≥75%计划剂量或发生DLT事件；PKCS需至少1个可评估PK浓度数据；PKPS需至少1个PK参数数据；C-QTcS需匹配的PK/ECG数据。",
    "数据来源于列表16.2.3。",
    "在各剂量组中，全分析集（FAS）纳入的受试者比例为100%（N=16/16）。有效性分析集（EAS）在2 mg组（N=1/1，100%）、5 mg组（N=1/1，100%）、10 mg组（N=3/4，75.0%）、20 mg组（N=3/3，100%）、30 mg组（N=3/3，100%）和40 mg组（N=2/4，50.0%）中分别纳入不同比例的受试者，总体纳入比例为81.3%（N=13/16）。"
  ],
  "tfl_table_content": [
    "表头名称：各患者（按肿瘤类型分）疗效评价游泳图（FAS）",
    "表头名称：不同肿瘤类型的最佳总疗效（FAS）",
    "表头名称：试验筛选情况（所有筛选患者）"
  ]
}
```

## 处理逻辑

### main_content 处理
1. 解析JSON字符串为列表
2. 清理文本（去除零宽空格等无用字符）
3. 按语义边界分割文本（每段约200字符）
4. 确保每个文本块以合适的边界结尾（句号或换行符）

### tfl_table_content 处理
1. 解析JSON字符串为表格对象列表
2. 提取每个表格的关键信息：
   - 表头名称（name字段）
   - 表格的首行名称（headers）
   - 脚注（footnote）
3. 格式化为文本字符串

## 错误处理

函数会抛出以下异常：
- `ValueError`: JSON解析错误
- `RuntimeError`: 处理过程中的其他错误

## 测试

运行测试脚本验证功能：

```bash
python test_process_content.py
```

运行使用示例：

```bash
python example_usage.py
```

## 文件结构

- `convert_to_separate_text.py` - 主要函数实现
- `test_process_content.py` - 测试脚本
- `example_usage.py` - 使用示例
- `data/data.json` - 测试数据
- `README_process_content.md` - 本说明文档

## 注意事项

1. 输入的两个参数必须是有效的JSON字符串
2. 函数不会输出任何文件，只返回处理后的数据
3. 返回的数据结构严格按照要求的格式：`{"main_content": [str], "tfl_table_content": [str]}`
4. main_content会被智能分割为语义完整的文本块
5. tfl_table_content会被转换为简洁的表格描述文本
