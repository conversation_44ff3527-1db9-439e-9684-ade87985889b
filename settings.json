"ruff.enable": true,
"ruff.nativeServer": "on",
"ruff.lineLength": 120,
"ruff.lint.select": [
  "E",    // pycodestyle errors
  "F",    // pyflakes
  "I",    // isort
  "W",    // pycodestyle warnings
  "N",    // pep8-naming
  "UP",   // pyupgrade
  "B",    // flake8-bugbear
  "C4",   // flake8-comprehensions
  "SIM",  // flake8-simplify
  "RUF",  // Ruff-specific rules
  "PL",   // pylint
  "C90"   // mccabe complexity
],
"ruff.lint.ignore": [
  "E501",   // line too long (handled by formatter)
  "PLR0913", // too many arguments
  "PLR0912", // too many branches
  "PLR2004", // magic value used in comparison
  "C901"     // function is too complex (adjust threshold instead)
],
"ruff.format.quoteStyle": "double",
"ruff.format.indentStyle": "space",
"ruff.format.skipMagicTrailingComma": false,
"ruff.lint.mccabe.maxComplexity": 15,
"ruff.lint.isort.knownFirstParty": ["abbr"],
"ruff.lint.pydocstyle.convention": "google"